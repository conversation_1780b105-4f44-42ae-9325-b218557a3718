import { PointsService } from '../pointsService';
import { supabase } from '@indie-points/lib';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('PointsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCustomerPointsSummary', () => {
    it('should return points data when successful', async () => {
      const mockData = [
        {
          total_earned: 1500,
          total_active: 750,
          total_redeemed: 750,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await PointsService.getCustomerPointsSummary('test-user-id');

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_points_summary',
        {
          p_customer_id: 'test-user-id',
        }
      );

      expect(result).toEqual({
        data: {
          totalEarned: 1500,
          totalActive: 750,
          totalRedeemed: 750,
        },
        error: null,
      });
    });

    it('should handle supabase errors', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await PointsService.getCustomerPointsSummary('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should handle empty data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await PointsService.getCustomerPointsSummary('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'No points data found for customer',
      });
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await PointsService.getCustomerPointsSummary('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });

    it('should handle missing values in response data', async () => {
      const mockData = [
        {
          total_earned: null,
          total_active: undefined,
          total_redeemed: 100,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await PointsService.getCustomerPointsSummary('test-user-id');

      expect(result).toEqual({
        data: {
          totalEarned: 0,
          totalActive: 0,
          totalRedeemed: 100,
        },
        error: null,
      });
    });
  });
});
