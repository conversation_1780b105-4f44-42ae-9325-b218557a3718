{"name": "customer-app", "private": true, "main": "expo-router/entry", "dependencies": {"@expo/html-elements": "*", "@expo/vector-icons": "*", "@gluestack-ui/accordion": "*", "@gluestack-ui/actionsheet": "*", "@gluestack-ui/alert": "*", "@gluestack-ui/alert-dialog": "*", "@gluestack-ui/avatar": "*", "@gluestack-ui/button": "*", "@gluestack-ui/checkbox": "*", "@gluestack-ui/divider": "*", "@gluestack-ui/fab": "*", "@gluestack-ui/form-control": "*", "@gluestack-ui/icon": "*", "@gluestack-ui/image": "*", "@gluestack-ui/input": "*", "@gluestack-ui/link": "*", "@gluestack-ui/menu": "*", "@gluestack-ui/modal": "*", "@gluestack-ui/nativewind-utils": "*", "@gluestack-ui/overlay": "*", "@gluestack-ui/popover": "*", "@gluestack-ui/pressable": "*", "@gluestack-ui/progress": "*", "@gluestack-ui/radio": "*", "@gluestack-ui/select": "*", "@gluestack-ui/slider": "*", "@gluestack-ui/spinner": "*", "@gluestack-ui/switch": "*", "@gluestack-ui/textarea": "*", "@gluestack-ui/toast": "*", "@gluestack-ui/tooltip": "*", "@legendapp/motion": "*", "@react-native-async-storage/async-storage": "*", "@react-navigation/bottom-tabs": "*", "@react-navigation/native": "*", "@supabase/supabase-js": "*", "babel-plugin-module-resolver": "*", "dayjs": "*", "expo": "*", "expo-auth-session": "*", "expo-blur": "*", "expo-camera": "*", "expo-constants": "*", "expo-crypto": "*", "expo-dev-client": "*", "expo-font": "*", "expo-haptics": "*", "expo-linking": "*", "expo-router": "*", "expo-splash-screen": "*", "expo-status-bar": "*", "expo-symbols": "*", "expo-system-ui": "*", "expo-web-browser": "*", "nativewind": "*", "react": "*", "react-dom": "*", "react-native": "*", "react-native-css-interop": "*", "react-native-gesture-handler": "*", "react-native-qrcode-svg": "*", "react-native-reanimated": "*", "react-native-safe-area-context": "*", "react-native-screens": "*", "react-native-svg": "*", "react-native-url-polyfill": "*", "react-native-web": "*", "react-native-webview": "*", "tailwindcss": "*"}, "devDependencies": {"@babel/core": "*", "@types/jest": "*", "@types/react": "*", "eas": "*", "eslint": "*", "eslint-config-expo": "*", "eslint-config-prettier": "*", "jest": "*", "jest-expo": "*", "jscodeshift": "*", "prettier": "*", "ts-jest": "*", "typescript": "*"}}