import React, { useState, useEffect } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Spinner } from '@indie-points/ui-spinner';
import { Alert, AlertText } from '@indie-points/ui-alert';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Input, InputField } from '@indie-points/ui-input';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  CameraView,
  CameraType,
  useCameraPermissions,
  BarcodeScanningResult,
} from 'expo-camera';
import { useAuth } from '@indie-points/contexts';
import { BusinessService, BusinessProfile } from '../../services';
import { GradientBar } from '@indie-points/auth';

// Types for customer QR code data
interface CustomerQRCodeData {
  expiry: number;
  issuedAt: number;
  userId: string;
}

export default function Scan() {
  const { user } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [facing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<CustomerQRCodeData | null>(
    null
  );
  const [scanCompleted, setScanCompleted] = useState(false);
  const [purchaseAmount, setPurchaseAmount] = useState('');
  const [showPurchaseForm, setShowPurchaseForm] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setErrorMessage(result.error);
        setBusinessProfile(null);
      } else if (!result.data) {
        setErrorMessage(
          'No business profile found. Please complete your business setup in Settings.'
        );
        setBusinessProfile(null);
      } else {
        setBusinessProfile(result.data);
      }

      setLoading(false);
    };

    fetchBusinessProfile();
  }, [user?.id]);

  // Parse customer QR code data
  const parseCustomerQRCodeData = (data: string): CustomerQRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.userId && parsed.expiry && parsed.issuedAt) {
        // Check if QR code is expired
        const now = Date.now();
        if (now > parsed.expiry) {
          return null; // Expired QR code
        }
        return {
          userId: parsed.userId,
          expiry: parsed.expiry,
          issuedAt: parsed.issuedAt,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing customer QR code data:', error);
      return null;
    }
  };

  // Handle customer QR code scan
  const handleBarCodeScanned = async (result: BarcodeScanningResult) => {
    if (isProcessing || !user?.id || scanCompleted || !businessProfile) return;

    const qrData = parseCustomerQRCodeData(result.data);
    if (!qrData) {
      setErrorMessage(
        'Invalid or expired customer QR code. Please ask the customer to refresh their QR code.'
      );
      setScanCompleted(true);
      return;
    }

    setScannedData(qrData);
    setShowPurchaseForm(true);
    setScanCompleted(true);
  };

  // Handle purchase transaction
  const handleCreatePurchase = async () => {
    if (!scannedData || !businessProfile || !purchaseAmount) return;

    const amount = parseFloat(purchaseAmount);
    if (isNaN(amount) || amount <= 0) {
      setErrorMessage('Please enter a valid purchase amount.');
      return;
    }

    setIsProcessing(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      const response = await BusinessService.createPurchaseTransaction({
        customerId: scannedData.userId,
        businessId: businessProfile.id,
        amountSpent: amount,
        qrToken: `${scannedData.userId}-${scannedData.issuedAt}`,
      });

      if (response.error) {
        setErrorMessage(response.error);
      } else if (response.data) {
        const pointsAwarded = response.data.pointsAwarded;
        setSuccessMessage(
          `Success! Customer earned ${pointsAwarded} points for £${amount.toFixed(2)} purchase!`
        );
        setPurchaseAmount('');
        setShowPurchaseForm(false);
      }
    } catch (error) {
      console.error('Error processing purchase transaction:', error);
      setErrorMessage('Failed to process purchase. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle scan again
  const handleScanAgain = () => {
    setScanCompleted(false);
    setIsProcessing(false);
    setSuccessMessage(null);
    setErrorMessage(null);
    setScannedData(null);
    setShowPurchaseForm(false);
    setPurchaseAmount('');
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Scan
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading business profile...
              </Text>
            </VStack>
          ) : (
            <VStack space='xl'>
              {/* Feedback Messages */}
              {successMessage && (
                <Alert action='success' variant='solid'>
                  <AlertText>{successMessage}</AlertText>
                </Alert>
              )}
              {errorMessage && (
                <Alert action='error' variant='solid'>
                  <AlertText>{errorMessage}</AlertText>
                </Alert>
              )}

              {/* Purchase Form */}
              {showPurchaseForm && scannedData && (
                <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                  <VStack space='lg'>
                    <Heading
                      size='lg'
                      className='text-typography-900 font-semibold text-center'
                    >
                      Process Purchase
                    </Heading>
                    <Text size='md' className='text-typography-600 text-center'>
                      Customer QR code scanned successfully. Enter the purchase
                      amount:
                    </Text>

                    <VStack space='md'>
                      <Text
                        size='md'
                        className='text-typography-900 font-medium'
                      >
                        Purchase Amount (£)
                      </Text>
                      <Input>
                        <InputField
                          placeholder='0.00'
                          value={purchaseAmount}
                          onChangeText={setPurchaseAmount}
                          keyboardType='decimal-pad'
                          autoFocus
                        />
                      </Input>
                    </VStack>

                    <HStack space='md'>
                      <Button
                        size='lg'
                        action='secondary'
                        className='flex-1'
                        onPress={handleScanAgain}
                      >
                        <ButtonText>Cancel</ButtonText>
                      </Button>
                      <Button
                        size='lg'
                        action='primary'
                        className='flex-1'
                        onPress={handleCreatePurchase}
                        disabled={isProcessing || !purchaseAmount}
                      >
                        {isProcessing ? (
                          <Spinner size='small' color='white' />
                        ) : (
                          <ButtonText>Confirm</ButtonText>
                        )}
                      </Button>
                    </HStack>
                  </VStack>
                </Box>
              )}

              {/* Camera section */}
              {!showPurchaseForm && (
                <VStack space='lg' className='items-center'>
                  <Heading
                    size='xl'
                    className='text-typography-900 font-semibold'
                  >
                    Scan customer QR code
                  </Heading>
                  <Text size='md' className='text-typography-600 text-center'>
                    Ask the customer to show their QR code from the Indie Points
                    app
                  </Text>
                  <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
                    {!permission ? (
                      <Text size='md' className='text-white'>
                        Requesting camera permission...
                      </Text>
                    ) : !permission.granted ? (
                      <VStack space='md' className='items-center'>
                        <FontAwesome name='camera' size={64} color='#fff' />
                        <Text size='md' className='text-white text-center'>
                          Camera access is required to scan customer QR codes.
                        </Text>
                      </VStack>
                    ) : scanCompleted ? (
                      <VStack
                        space='lg'
                        className='items-center justify-center flex-1'
                      >
                        <FontAwesome
                          name='check-circle'
                          size={64}
                          color='#22c55e'
                        />
                        <Text
                          size='lg'
                          className='text-white text-center font-semibold'
                        >
                          Customer QR code scanned!
                        </Text>
                        <Button
                          size='lg'
                          action='primary'
                          className='mt-4 bg-primary-500'
                          onPress={handleScanAgain}
                        >
                          <ButtonText>Scan another</ButtonText>
                        </Button>
                      </VStack>
                    ) : (
                      <CameraView
                        style={{ width: '100%', aspectRatio: 1 }}
                        facing={facing}
                        ratio='1:1'
                        barcodeScannerSettings={{
                          barcodeTypes: ['qr'],
                        }}
                        onBarcodeScanned={handleBarCodeScanned}
                      />
                    )}
                  </Box>
                </VStack>
              )}

              {/* How to Process Purchases Section */}
              <VStack space='lg'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  How to process customer purchases
                </Heading>

                {/* Step 1 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      1
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Ask customer for their QR code
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Customer opens the Indie Points app and shows their QR
                      code
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 2 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      2
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Scan and enter purchase amount
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Use this camera to scan the customer&apos;s QR code and
                      enter the purchase amount
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 3 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      3
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Complete the transaction
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Points are automatically awarded based on your business
                      settings
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
