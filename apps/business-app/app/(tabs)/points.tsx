import React, { useState, useCallback, useEffect, useRef } from 'react';
import { ScrollView, Alert } from 'react-native';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Spinner } from '@indie-points/ui-spinner';
import { Button, ButtonText } from '@indie-points/ui-button';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@indie-points/contexts';
import { BusinessService, BusinessProfile } from '../../services';
import QRCode from 'react-native-qrcode-svg';
import { GradientBar } from '@indie-points/auth';
import * as Print from 'expo-print';

export default function Points() {
  const { user } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [qrWidth, setQrWidth] = useState(0);
  const [isPrinting, setIsPrinting] = useState(false);
  const [qrCodeBase64, setQrCodeBase64] = useState<string | null>(null);
  const qrRef = useRef<any>(null);

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setError(result.error);
        setBusinessProfile(null);
      } else if (!result.data) {
        setError(
          'No business profile found. Please complete your business setup in Settings.'
        );
        setBusinessProfile(null);
      } else {
        setBusinessProfile(result.data);
        setError(null);
      }

      setLoading(false);
    };

    fetchBusinessProfile();
  }, [user?.id]);

  // Helper to generate business QR code payload
  const generateBusinessQrPayload = useCallback(() => {
    if (!businessProfile) return '';

    const now = Date.now();
    return JSON.stringify({
      businessId: businessProfile.id,
      businessName: businessProfile.businessName,
      token: `${businessProfile.id}-${now}`, // Simple token generation
    });
  }, [businessProfile]);

  // Generate base64 QR code for printing
  const generateQRBase64 = useCallback(() => {
    if (qrRef.current && businessProfile) {
      qrRef.current.toDataURL((dataURL: string) => {
        setQrCodeBase64(dataURL);
      });
    }
  }, [businessProfile]);

  // Generate base64 when QR data is available
  useEffect(() => {
    if (businessProfile && qrWidth > 0 && !qrCodeBase64) {
      // Wait for QR component to be fully rendered
      const timer = setTimeout(() => {
        if (qrRef.current) {
          generateQRBase64();
        }
      }, 500); // Increased delay to ensure QR is rendered

      return () => clearTimeout(timer);
    }
  }, [businessProfile, qrWidth, qrCodeBase64, generateQRBase64]);

  // Create HTML content for printing
  const createPrintHTML = (): string => {
    if (!businessProfile?.businessName || !qrCodeBase64) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Indie Points - Visit QR Code</title>
          <style>
            @page {
              size: A4;
              margin: 40px;
            }
            @media print {
              .no-print {
                display: none !important;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 0;
              background: white;
              color: #000000;
              line-height: 1.6;
            }
            .container {
              max-width: 100%;
              margin: 0 auto;
              text-align: center;
              padding: 20px;
            }
            .brand-bar {
              display: flex;
              height: 8px;
              margin-bottom: 30px;
              border: 2px solid #000000;
            }
            .brand-bar-blue {
              flex: 1;
              background-color: #3182CE !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .brand-bar-yellow {
              flex: 1;
              background-color: #D69E2E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .brand-bar-red {
              flex: 1;
              background-color: #E53E3E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .title {
              font-size: 48px;
              font-weight: bold;
              margin-bottom: 10px;
              color: #000000;
            }
            .subtitle {
              font-size: 24px;
              color: #718096;
              margin-bottom: 40px;
            }
            .business-name {
              font-size: 32px;
              font-weight: bold;
              color: #3182CE;
              margin-bottom: 30px;
            }
            .qr-container {
              margin: 40px 0;
              display: flex;
              justify-content: center;
            }
            .instructions {
              font-size: 20px;
              color: #2D3748;
              margin-top: 40px;
              text-align: left;
              max-width: 600px;
              margin-left: auto;
              margin-right: auto;
            }
            .instruction-step {
              margin-bottom: 15px;
              padding-left: 30px;
              position: relative;
            }
            .step-number {
              position: absolute;
              left: 0;
              top: 0;
              color: white;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 14px;
            }
            .step-number-1 {
              background: #3182CE !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .step-number-2 {
              background: #D69E2E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .step-number-3 {
              background: #E53E3E !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .footer {
              margin-top: 60px;
              font-size: 16px;
              color: #718096;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <!-- Brand bar -->
            <div class="brand-bar">
              <div class="brand-bar-blue"></div>
              <div class="brand-bar-yellow"></div>
              <div class="brand-bar-red"></div>
            </div>
            
            <!-- Header -->
            <h1 class="title">Indie Points</h1>
            <p class="subtitle">Earn points, get rewards, support local businesses</p>
            
            <!-- Business name -->
            <h2 class="business-name">${businessProfile.businessName}</h2>
            
            <!-- QR Code -->
            <div class="qr-container">
              <div style="width: 300px; height: 300px; border: 2px solid #000; display: flex; align-items: center; justify-content: center; padding: 20px; box-sizing: border-box;">
                <img src="data:image/png;base64,${qrCodeBase64}" alt="Visit QR Code" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
              </div>
            </div>
            
            <!-- Instructions -->
            <div class="instructions">
              <div class="instruction-step">
                <div class="step-number step-number-1">1</div>
                Download the Indie Points app from your app store
              </div>
              <div class="instruction-step">
                <div class="step-number step-number-2">2</div>
                Open the app and scan this QR code with your camera
              </div>
              <div class="instruction-step">
                <div class="step-number step-number-3">3</div>
                Earn your free visit point instantly!
              </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
              <p>Visit points can only be claimed once per business.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  };

  // Handle print
  const handlePrint = async () => {
    if (!businessProfile) {
      Alert.alert('Error', 'Business profile not found');
      return;
    }

    // Try to generate QR base64 if not available
    if (!qrCodeBase64 && qrRef.current) {
      qrRef.current.toDataURL((dataURL: string) => {
        setQrCodeBase64(dataURL);
        // Retry print after base64 is generated
        setTimeout(() => {
          handlePrint();
        }, 100);
      });
      return;
    }

    if (!qrCodeBase64) {
      Alert.alert(
        'Error',
        'QR code image not ready. Please wait a moment and try again.'
      );
      return;
    }

    setIsPrinting(true);
    try {
      const htmlContent = createPrintHTML();

      await Print.printAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
      });
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert('Print Error', 'Failed to print. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Points
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading your business QR code...
              </Text>
            </VStack>
          ) : error ? (
            // Error state
            <VStack space='lg' className='items-center py-8'>
              <FontAwesome
                name='exclamation-triangle'
                size={48}
                color='#ef4444'
              />
              <Text
                size='lg'
                className='text-error-500 font-semibold text-center'
              >
                Unable to load QR code
              </Text>
              <Text size='md' className='text-typography-600 text-center'>
                {error}
              </Text>
            </VStack>
          ) : (
            <VStack space='xl'>
              {/* Business QR code section */}
              <VStack space='lg' className='items-center'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  Your business QR code
                </Heading>
                <Text size='md' className='text-typography-600 text-center'>
                  Display this code for customers to scan and earn points
                </Text>

                {/* QR Code */}
                <Box
                  className='w-full max-w-md aspect-square bg-white rounded-2xl border-4 p-4 border-typography-900 items-center justify-center shadow-lg'
                  onLayout={event => {
                    const width = event.nativeEvent.layout.width;
                    setQrWidth(width);
                  }}
                >
                  <VStack space='md' className='items-center w-full h-full'>
                    {businessProfile && qrWidth > 0 ? (
                      <QRCode
                        value={generateBusinessQrPayload()}
                        size={Math.max(qrWidth - 8 - 32, 0)}
                        logo={require('../../assets/images/icon.png')}
                        logoSize={Math.max((qrWidth - 8 - 32) * 0.2, 32)}
                        logoBackgroundColor='transparent'
                        getRef={c => (qrRef.current = c)}
                      />
                    ) : (
                      <FontAwesome name='qrcode' size={120} color='#000' />
                    )}
                  </VStack>
                </Box>

                {/* Print Button */}
                <Button
                  size='lg'
                  className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
                  onPress={handlePrint}
                  disabled={isPrinting}
                >
                  <HStack space='sm' className='items-center'>
                    <FontAwesome name='print' size={16} color='white' />
                    <ButtonText className='text-white font-semibold'>
                      {isPrinting ? 'Printing...' : 'Print'}
                    </ButtonText>
                  </HStack>
                </Button>
              </VStack>

              {/* How to Use Your QR Code Section */}
              <VStack space='lg'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  How customers earn points
                </Heading>

                {/* Step 1 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      1
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Display your QR code
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Show this QR code prominently in your business for
                      customers to see
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 2 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      2
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Customers scan your code
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Customers use the Indie Points app to scan your QR code
                      when they visit
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 3 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      3
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Points awarded automatically
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Customers earn 1 point for visiting, plus points for
                      purchases you process
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
